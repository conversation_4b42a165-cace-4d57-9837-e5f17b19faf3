package com.erhgo.services.scrapeduser;

import com.erhgo.config.ConfigurableProperty;
import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgefiphScraper {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RetryableHttpClient retryableHttpClient;
    private final ConfigurablePropertyRepository configurablePropertyRepository;

    @Value("${agefiph.api.search-url}")
    private String searchUrl;

    @Value("${agefiph.api.base-url}")
    private String baseUrl;

    @Value("${agefiph.scraping.page-size}")
    private int pageSize;

    @Value("${agefiph.scraping.max-pages-per-session}")
    private int maxPagesPerSession;

    @Value("${agefiph.scraping.max-total-candidates}")
    private int maxTotalCandidates;

    private static final String AUTH_TOKEN_COOKIE = "company.security.authtoken";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final String COOKIE = "Cookie";
    private static final String CANDIDATE_API_PATH = "/api/company/candidates/%s";
    private static final String LAST_SCRAPED_PAGE_KEY = "agefiph.scraping.last.page";
    private static final String CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private static final String BUILD_SEARCH_PAYLOAD = """
            {
                "wishedLocationFilter": false,
                "addressLocationFilter": true,
                "includeSynonyms": true,
                "includeCandidatesViewedByUser": true,
                "includeCandidatesUnViewedByUser": true,
                "includeCandidatesWithoutSalary": true,
                "includeAvailabilityDate": false,
                "customFieldItems": {},
                "queryType": "MATCHING",
                "query": "",
                "locations": [
                    {
                        "geonameId": 2996944,
                        "radius": 30,
                        "admin1": 11071625,
                        "admin2": 2987410,
                        "admin3": 6454573,
                        "country": 3017382,
                        "label": "Lyon (69)",
                        "centerLat": 45.74846,
                        "centerLon": 4.84671
                    }
                ],
                "filters": [
                    {
                        "key": "lastLogin_agg",
                        "values": ["lastLogin_from_now-24M_to_now"]
                    }
                ],
                "facets": [],
                "matching": {
                    "minMatch": 0,
                    "experienceJobs": [],
                    "specializations": [],
                    "accreditationMainIds": [],
                    "languages": [],
                    "degrees": [],
                    "contractTypes": [],
                    "jobTypes": [],
                    "licenses": [],
                    "industryFields": []
                }
            }
            """;

    /**
     * Auth token retrieval instructions:
     * 1. Navigate to https://espace-emploi.agefiph.fr/company/signin
     * 2. Login with email: <EMAIL>
     * 3. Get token either:
     * a) From DevTools > Network > find /login request > Headers > set-cookie > company.security.authtoken
     * b) From DevTools > Application >COOKIE > agefiph > company.security.authtoken
     * 4. Store this token in the configurableProperty table with key "agefiph.auth.token"
     */
    private String getAuthToken() {
        var property = configurablePropertyRepository.findOneByPropertyKey("agefiph.auth.token");
        return property != null ? property.getPropertyValue() : null;
    }

    public List<ScrapedCandidate> searchCandidates() throws InvalidScrapingException {
        var authToken = validateAndGetAuthToken();

        try {
            log.debug("Searching candidates at URL: {}", searchUrl);

            var requestBody = RequestBody.create(BUILD_SEARCH_PAYLOAD, JSON);

            var request = new Request.Builder()
                    .url(searchUrl)
                    .post(requestBody)
                    .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                    .addHeader(COOKIE, AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var responseBody = response.body().string();
                return parseCandidatesFromJsonResponse(responseBody);
            }
        } catch (RuntimeException | IOException e) {
            log.warn("Technical error during AGEFIPH API candidate search: {}", e.getMessage(), e);
            throw new InvalidScrapingException(e);
        }
    }

    private List<ScrapedCandidate> parseCandidatesFromJsonResponse(String responseBody) throws InvalidScrapingException {
        var candidates = new ArrayList<ScrapedCandidate>();

        try {
            var jsonNode = objectMapper.readTree(responseBody);
            var itemsNode = jsonNode.path("items");

            if (!itemsNode.isArray()) {
                log.warn("No items array found in AGEFIPH API response");
                return candidates;
            }

            log.debug("Found {} candidate items in AGEFIPH API response", itemsNode.size());

            StreamSupport.stream(itemsNode.spliterator(), false)
                    .map(this::parseCandidateFromJsonNode)
                    .filter(Objects::nonNull)
                    .forEach(candidates::add);

            log.info("Successfully parsed {} scraped candidates from AGEFIPH API response", candidates.size());
        } catch (JsonProcessingException e) {
            log.error("Error parsing scraped candidates from AGEFIPH API response", e);
            throw new InvalidScrapingException("Failed to parse JSON response", e);
        }

        return candidates;
    }

    private ScrapedCandidate parseCandidateFromJsonNode(JsonNode candidateNode) {
        try {
            var candidateId = candidateNode.path("id").asText();
            if (candidateId.isEmpty()) {
                log.debug("Skipping candidate with empty ID");
                return null;
            }

            var email = candidateNode.path("email").asText();
            var hasResume = candidateNode.path("hasResume").asBoolean();


            var cvDownloadLink = baseUrl + CANDIDATE_API_PATH.formatted(candidateId) + "/resume/download";
            var profileUrl = baseUrl + CANDIDATE_API_PATH.formatted(candidateId);

            log.debug("Parsed candidate - ID: {}, Email: {}", candidateId, email);

            return ScrapedCandidate.builder()
                    .candidateId(candidateId)
                    .email(email)
                    .hasResume(hasResume)
                    .cvDownloadLink(cvDownloadLink)
                    .profileUrl(profileUrl)
                    .build();
        } catch (RuntimeException e) {
            log.error("Error parsing candidate from JSON node: {}", e.getMessage(), e);
            return null;
        }
    }

    public ScrapedCandidate getCandidateDetails(String candidateId) throws InvalidScrapingException {
        var authToken = validateAndGetAuthToken();

        try {
            var detailsUrl = baseUrl + CANDIDATE_API_PATH.formatted(candidateId);
            log.debug("Fetching candidate details from URL: {}", detailsUrl);

            var request = new Request.Builder()
                    .url(detailsUrl)
                    .get()
                    .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                    .addHeader(COOKIE, AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var responseBody = response.body().string();
                return parseCandidateDetailsFromJson(responseBody);
            }
        } catch (RuntimeException | IOException e) {
            log.warn("Technical error during AGEFIPH API candidate details fetch: {}", e.getMessage(), e);
            throw new InvalidScrapingException(e);
        }
    }

    private ScrapedCandidate parseCandidateDetailsFromJson(String responseBody) throws InvalidScrapingException {
        try {
            var detailsNode = objectMapper.readTree(responseBody);

            var candidateId = detailsNode.path("id").asText();
            var firstName = detailsNode.path("firstName").asText();
            var lastName = detailsNode.path("name").asText();
            var email = detailsNode.path("login").asText();

            var addressLocationNode = detailsNode.path("addressLocation");
            var location = addressLocationNode.path("label").asText();
            var jobTitle = extractJobTitle(detailsNode);

            var cvDownloadLink = baseUrl + CANDIDATE_API_PATH.formatted(candidateId) + "/resume/download";
            var profileUrl = baseUrl + CANDIDATE_API_PATH.formatted(candidateId);

            log.debug("Parsed candidate details - ID: {}, Name: {} {}, Email: {}, Job Title: {}, Location: {}",
                    candidateId, firstName, lastName, email, jobTitle, location);

            return ScrapedCandidate.builder()
                    .candidateId(candidateId)
                    .firstName(firstName)
                    .lastName(lastName)
                    .email(email)
                    .jobTitle(jobTitle)
                    .location(location)
                    .cvDownloadLink(cvDownloadLink)
                    .profileUrl(profileUrl)
                    .build();
        } catch (JsonProcessingException e) {
            log.error("Error parsing candidate details from AGEFIPH API response", e);
            throw new InvalidScrapingException("Failed to parse candidate details JSON response", e);
        }
    }

    private String extractJobTitle(JsonNode candidateNode) {
        var jobsNode = candidateNode.path("jobs");
        if (jobsNode.isArray() && !jobsNode.isEmpty()) {
            return jobsNode.get(0).path("value").asText();
        }
        return "";
    }

    private String validateAndGetAuthToken() throws InvalidScrapingException {
        var authToken = getAuthToken();
        if (StringUtils.isBlank(authToken)) {
            log.warn("AGEFIPH auth token not configured in database");
            throw new InvalidScrapingException("AGEFIPH auth token not configured");
        }
        return authToken;
    }

    /**
     * Nouveau workflow de scrapping en deux étapes :
     * Étape 1 : Récupérer les nouveaux candidats (première page)
     * Étape 2 : Rattraper les anciens candidats à partir de la page sauvegardée
     */
    public List<ScrapedCandidate> searchCandidatesTwoStepWorkflow() throws InvalidScrapingException {
        var authToken = validateAndGetAuthToken();
        var allCandidates = new ArrayList<ScrapedCandidate>();

        log.info("Starting two-step AGEFIPH candidate scraping workflow");

        try {
            // Étape 1 : Récupérer les nouveaux candidats (page 0)
            log.info("Step 1: Searching for new candidates from page 0");
            var newCandidatesResult = searchCandidatesFromPage(authToken, 0);
            var newCandidates = newCandidatesResult.candidates();
            var totalCount = newCandidatesResult.totalCount();

            log.info("Found {} new candidates on first page, total candidates available: {}",
                    newCandidates.size(), totalCount);

            allCandidates.addAll(newCandidates);

            // Si on a déjà atteint le maximum, on s'arrête
            if (allCandidates.size() >= maxTotalCandidates) {
                log.info("Reached maximum candidates limit with new candidates only: {}", allCandidates.size());
                return limitCandidates(allCandidates);
            }

            // Étape 2 : Rattrapage à partir de la page sauvegardée
            var lastScrapedPage = getLastScrapedPage();
            log.info("Step 2: Starting catch-up from saved page {}", lastScrapedPage);

            var maxPage = Math.min(
                    (totalCount / pageSize) + 1, // Dernière page possible
                    lastScrapedPage + maxPagesPerSession // Limite de pages par session
            );

            var pagesProcessed = new AtomicInteger(0);
            var shouldStop = new AtomicBoolean(false);

            // Utiliser un stream pour traiter les pages de rattrapage
            IntStream.rangeClosed(lastScrapedPage, maxPage)
                    .filter(currentPage -> currentPage != 0) // Éviter de re-scrapper la page 0
                    .takeWhile(currentPage -> !shouldStop.get() &&
                            pagesProcessed.get() < maxPagesPerSession &&
                            allCandidates.size() < maxTotalCandidates)
                    .forEach(currentPage -> {
                        try {
                            log.debug("Processing catch-up page {}", currentPage);
                            var catchupResult = searchCandidatesFromPage(authToken, currentPage);
                            var catchupCandidates = catchupResult.candidates();

                            allCandidates.addAll(catchupCandidates);
                            pagesProcessed.incrementAndGet();

                            // Sauvegarder la dernière page traitée
                            saveLastScrapedPage(currentPage);

                            log.debug("Added {} candidates from page {}, total so far: {}",
                                    catchupCandidates.size(), currentPage, allCandidates.size());

                            // Si on a atteint le maximum de candidats, on s'arrête
                            if (allCandidates.size() >= maxTotalCandidates) {
                                log.info("Reached maximum candidates limit: {}", allCandidates.size());
                                shouldStop.set(true);
                                return;
                            }

                            // Si on est à la dernière page possible, on remet le compteur à 1 pour la prochaine fois
                            if (currentPage >= (totalCount / pageSize)) {
                                log.info("Reached last page, resetting last scraped page to 1 for next session");
                                saveLastScrapedPage(1);
                                shouldStop.set(true);
                            }

                        } catch (IOException | InvalidScrapingException e) {
                            log.error("Error processing page {}: {}", currentPage, e.getMessage(), e);
                            shouldStop.set(true);
                        }
                    });

            log.info("Two-step scraping completed. Total candidates found: {}, Pages processed: {}",
                    allCandidates.size(), pagesProcessed.get() + 1); // +1 pour la page 0

            return limitCandidates(allCandidates);

        } catch (RuntimeException | IOException e) {
            log.error("Error during two-step AGEFIPH candidate scraping: {}", e.getMessage(), e);
            throw new InvalidScrapingException("Failed to execute two-step scraping workflow", e);
        }
    }

    private CandidateSearchResult searchCandidatesFromPage(String authToken, int page) throws IOException, InvalidScrapingException {
        var from = page * pageSize;
        var urlWithPagination = searchUrl.replaceAll("from=\\d+", "from=" + from)
                .replaceAll("size=\\d+", "size=" + pageSize);

        log.debug("Searching candidates from page {} (from={}), URL: {}", page, from, urlWithPagination);

        var requestBody = RequestBody.create(BUILD_SEARCH_PAYLOAD, JSON);
        var request = new Request.Builder()
                .url(urlWithPagination)
                .post(requestBody)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .addHeader(COOKIE, AUTH_TOKEN_COOKIE + "=" + authToken)
                .build();

        try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
            var responseBody = response.body().string();
            return parseCandidatesFromJsonResponseWithTotal(responseBody);
        }
    }

    private CandidateSearchResult parseCandidatesFromJsonResponseWithTotal(String responseBody) throws InvalidScrapingException {
        var candidates = new ArrayList<ScrapedCandidate>();
        var totalCount = 0;

        try {
            var jsonNode = objectMapper.readTree(responseBody);
            var itemsNode = jsonNode.path("items");

            // Récupérer le nombre total de candidats
            totalCount = jsonNode.path("total").asInt();

            if (!itemsNode.isArray()) {
                log.warn("No items array found in AGEFIPH API response");
                return new CandidateSearchResult(candidates, totalCount);
            }

            log.debug("Found {} candidate items in AGEFIPH API response, total: {}", itemsNode.size(), totalCount);

            StreamSupport.stream(itemsNode.spliterator(), false)
                    .map(this::parseCandidateFromJsonNode)
                    .filter(Objects::nonNull)
                    .forEach(candidates::add);

            log.debug("Successfully parsed {} scraped candidates from AGEFIPH API response", candidates.size());
        } catch (JsonProcessingException e) {
            log.error("Error parsing scraped candidates from AGEFIPH API response", e);
            throw new InvalidScrapingException("Failed to parse JSON response", e);
        }

        return new CandidateSearchResult(candidates, totalCount);
    }

    private List<ScrapedCandidate> limitCandidates(List<ScrapedCandidate> candidates) {
        if (candidates.size() <= maxTotalCandidates) {
            return candidates;
        }
        log.info("Limiting candidates from {} to {}", candidates.size(), maxTotalCandidates);
        return candidates.subList(0, maxTotalCandidates);
    }

    private int getLastScrapedPage() {
        var property = configurablePropertyRepository.findOneByPropertyKey(LAST_SCRAPED_PAGE_KEY);
        if (property != null && StringUtils.isNotBlank(property.getPropertyValue())) {
            try {
                return Integer.parseInt(property.getPropertyValue());
            } catch (NumberFormatException e) {
                log.warn("Invalid last scraped page value: {}, defaulting to 1", property.getPropertyValue());
            }
        }
        return 1; // Par défaut, commencer à la page 1 (page 0 = nouveaux candidats)
    }

    private void saveLastScrapedPage(int page) {
        var property = configurablePropertyRepository.findOneByPropertyKey(LAST_SCRAPED_PAGE_KEY);
        if (property != null) {
            property.setPropertyValue(String.valueOf(page));
            configurablePropertyRepository.save(property);
        } else {
            // Créer une nouvelle propriété si elle n'existe pas
            var newProperty = new ConfigurableProperty()
                    .setPropertyKey(LAST_SCRAPED_PAGE_KEY)
                    .setPropertyValue(String.valueOf(page));
            configurablePropertyRepository.save(newProperty);
        }
        log.debug("Saved last scraped page: {}", page);
    }

    private record CandidateSearchResult(List<ScrapedCandidate> candidates, int totalCount) {
    }
}
