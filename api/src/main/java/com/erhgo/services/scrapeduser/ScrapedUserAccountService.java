package com.erhgo.services.scrapeduser;

import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.domain.userprofile.ScrapedUser;
import com.erhgo.domain.userprofile.UserProfileCreationState;
import com.erhgo.openapi.dto.CreateAgefiphAccountCommandDTO;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.repositories.ScrapedUserRepository;
import com.erhgo.services.HandicapAccountService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;


@Slf4j
@Service
@RequiredArgsConstructor
public class ScrapedUserAccountService {

    private final ScrapedUserRepository scrapedUserRepository;
    private final AgefiphScraper agefiphScraper;
    private final HandicapAccountService handicapAccountService;
    private final KeycloakService keycloakService;
    private final ConfigurablePropertyRepository configurablePropertyRepository;
    private final MailingListService sendInBlueService;


    private static final String AUTH_TOKEN_COOKIE = "company.security.authtoken";

    /**
     * How to retrieve the AGEFIPH auth token:
     * Commented in AgefiphScraper.java
     */
    private String getAuthToken() {
        var property = configurablePropertyRepository.findOneByPropertyKey("agefiph.auth.token");
        return property != null ? property.getPropertyValue() : null;
    }

    public void createAgefiphCandidateAccount(CreateAgefiphAccountCommandDTO command) {
        createUserAccount(command.getCandidateId(), command.getScrapedUserId());
    }

    private void createUserAccount(String candidateId, UUID scrapedUserId) {
        log.info("Starting account creation for AGEFIPH candidate {} (scraped user {})", candidateId, scrapedUserId);
        var scrapedUser = scrapedUserRepository.findById(scrapedUserId)
                .orElseThrow(() -> new EntityNotFoundException(scrapedUserId, ScrapedUser.class));
        try {
            if (!validateCandidateId(candidateId, scrapedUser)) return;
            if (accountAlreadyExists(scrapedUser)) return;

            var candidateDetails = agefiphScraper.getCandidateDetails(candidateId);
            if (candidateDetails == null) {
                updateScrapedUserError(scrapedUser, "Failed to retrieve candidate details");
                return;
            }

            updateScrapedUserWithFullData(scrapedUser, candidateDetails);
            createActualUserAccount(scrapedUser);

        } catch (InvalidScrapingException e) {
            log.error("Failed to retrieve candidate details for {}: {}", candidateId, e.getMessage(), e);
            updateScrapedUserError(scrapedUser, "Failed to retrieve candidate details: " + e.getMessage());
        } catch (RuntimeException e) {
            log.error("Unexpected error creating account for AGEFIPH candidate {}: {}", candidateId, e.getMessage(), e);
            updateScrapedUserError(scrapedUser, "Unexpected error: " + e.getMessage());
        }
    }

    private boolean validateCandidateId(String candidateId, ScrapedUser scrapedUser) {
        if (!candidateId.equals(scrapedUser.getCandidateId())) {
            updateScrapedUserError(scrapedUser, "Candidate ID mismatch: expected %s, got %s".formatted(candidateId, scrapedUser.getCandidateId()));
            return false;
        }
        return true;
    }

    private boolean accountAlreadyExists(ScrapedUser scrapedUser) {
        if (scrapedUser.getEmail() != null) {
            var existingUser = keycloakService.getFOUserRepresentationByEmail(scrapedUser.getEmail());
            if (existingUser != null) {
                log.info("Account already exists for candidate {} with email {}", scrapedUser.getCandidateId(), scrapedUser.getEmail());
                scrapedUser.setCreationState(UserProfileCreationState.ALREADY_EXISTS);
                scrapedUserRepository.save(scrapedUser);
                return true;
            }
        }
        return false;
    }

    private void updateScrapedUserError(ScrapedUser scrapedUser, String errorMsg) {
        log.error(errorMsg);
        scrapedUser
                .setCreationState(UserProfileCreationState.ERROR)
                .setErrorMessage(errorMsg);
        scrapedUserRepository.save(scrapedUser);
    }


    private void updateScrapedUserWithFullData(ScrapedUser scrapedUser, ScrapedCandidate candidateData) {
        scrapedUser
                .setFirstName(candidateData.getFirstName())
                .setLastName(candidateData.getLastName())
                .setEmail(candidateData.getEmail())
                .setJobTitle(candidateData.getJobTitle())
                .setLocation(candidateData.getLocation())
                .setCvDownloadLink(candidateData.getCvDownloadLink())
                .setProfileUrl(candidateData.getProfileUrl());
    }


    public void addCandidateToBrevoList(ScrapedUser scrapedUser) {
        log.info("Adding scraped user {} to AGEFIPH candidates Brevo list", scrapedUser.getCandidateId());

        if (StringUtils.isBlank(scrapedUser.getEmail())) {
            log.warn("No email found for scraped user {}, skipping Brevo list addition", scrapedUser.getCandidateId());
            return;
        }

        sendInBlueService.addContactToAgefiphCandidatesList(
                scrapedUser.getEmail(),
                scrapedUser.getUuid().toString(),
                scrapedUser.getCandidateId()
        );

        scrapedUser.setCreationState(UserProfileCreationState.PENDING);
        scrapedUserRepository.save(scrapedUser);

        log.info("Successfully added scraped user {} to AGEFIPH candidates Brevo list", scrapedUser.getCandidateId());
    }


    private void createActualUserAccount(ScrapedUser scrapedUser) {
        if (StringUtils.isBlank(scrapedUser.getEmail())) {
            return;
        }
        try {
            var request = createAuthenticatedRequest(scrapedUser.getCvDownloadLink());
            handicapAccountService.createOrUpdateUserForFileURL(scrapedUser.getEmail(), request);
            scrapedUser.setCreationState(UserProfileCreationState.CREATED);
            scrapedUserRepository.save(scrapedUser);
            log.info("Successfully created account for AGEFIPH candidate {}", scrapedUser.getCandidateId());
        } catch (RuntimeException e) {
            log.error("Error creating user account for scraped user {}: {}", scrapedUser.getCandidateId(), e.getMessage(), e);
            updateScrapedUserError(scrapedUser, "Failed to create user account: " + e.getMessage());
        }
    }

    private Optional<Request> createAuthenticatedRequest(String cvDownloadLink) {
        if (StringUtils.isBlank(cvDownloadLink)) {
            return Optional.empty();
        }

        var authToken = getAuthToken();
        if (StringUtils.isBlank(authToken)) {
            log.warn("AGEFIPH auth token not configured in database");
            return Optional.empty();
        }

        var request = new Request.Builder()
                .url(cvDownloadLink)
                .get()
                .addHeader("Accept", "application/json")
                .addHeader("Cookie", AUTH_TOKEN_COOKIE + "=" + authToken)
                .build();

        return Optional.of(request);
    }
}
