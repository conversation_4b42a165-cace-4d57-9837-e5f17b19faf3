package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.ScrapedUser;
import com.erhgo.openapi.dto.CreateAgefiphAccountCommandDTO;
import com.erhgo.openapi.dto.ScrapedUserDTO;
import com.erhgo.repositories.ScrapedUserRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.SecurityService;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.scrapeduser.ScrapedUserAccountService;
import com.erhgo.services.scrapeduser.ScrapedUserService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class ScrapedUserControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    private MailingListService mailingListService;

    @MockitoBean
    private KeycloakMockService keycloakService;

    @MockitoBean
    private ScrapedUserService scrapedUserService;

    @MockitoBean
    private ScrapedUserAccountService scrapedUserAccountService;


    @MockitoBean
    private SecurityService securityService;

    @Autowired
    private ScrapedUserRepository scrapedUserRepository;


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void getScrapedUsers_should_succeed() {
        var scrapedUsers = prepareScrapedUsers();
        Mockito.when(scrapedUserService.getAllScrapedUsers()).thenReturn(scrapedUsers);
        performGetAndExpect("/api/odas/scraped-users", "scrapedUsersList", false);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void exportScrapedUsers_should_succeed() throws Exception {
        txHelper.doInTransaction(() -> {
            var scrapedUsers = prepareScrapedUsers();
            scrapedUsers.stream().map(dto -> new ScrapedUser()
                            .setUuid(dto.getUuid())
                            .setCandidateId(dto.getCandidateId())
                            .setFirstName(dto.getFirstName())
                            .setLastName(dto.getLastName())
                            .setEmail(dto.getEmail())
                            .setJobTitle(dto.getJobTitle())
                            .setLocation(dto.getLocation())
                            .setCvContent(dto.getCvContent())
                            .setCvDownloadLink(dto.getCvDownloadLink())
                            .setProfileUrl(dto.getProfileUrl()))
                    .forEach(scrapedUserRepository::save);
        });

        mvc.perform(MockMvcRequestBuilders.get("/api/odas/scraped-users/export"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void getScrapedUserDetail_should_succeed() throws Exception {
        var uuid = UUID.randomUUID();
        var scrapedUser = new ScrapedUserDTO()
                .uuid(uuid)
                .candidateId("CAND001")
                .firstName("John")
                .lastName("Doe")
                .email("<EMAIL>")
                .jobTitle("Software Developer")
                .location("Paris")
                .cvContent("{\"email\":\"<EMAIL>\",\"firstName\":\"John\",\"lastName\":\"Doe\"}")
                .cvDownloadLink("http://example.com/cv1.pdf")
                .profileUrl("http://example.com/profile/CAND001");

        Mockito.when(scrapedUserService.getScrapedUserDetail(uuid)).thenReturn(scrapedUser);

        mvc.perform(MockMvcRequestBuilders.get("/api/odas/scraped-users/" + uuid))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json"))
                .andExpect(jsonPath("$.uuid", is(uuid.toString())))
                .andExpect(jsonPath("$.candidateId", is("CAND001")))
                .andExpect(jsonPath("$.firstName", is("John")))
                .andExpect(jsonPath("$.lastName", is("Doe")))
                .andExpect(jsonPath("$.email", is("<EMAIL>")))
                .andExpect(jsonPath("$.jobTitle", is("Software Developer")))
                .andExpect(jsonPath("$.location", is("Paris")))
                .andExpect(jsonPath("$.cvContent", is("{\"email\":\"<EMAIL>\",\"firstName\":\"John\",\"lastName\":\"Doe\"}")))
                .andExpect(jsonPath("$.cvDownloadLink", is("http://example.com/cv1.pdf")))
                .andExpect(jsonPath("$.profileUrl", is("http://example.com/profile/CAND001")));

        Mockito.verify(scrapedUserService).getScrapedUserDetail(uuid);
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void exportScrapedUsers_should_set_correct_headers() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/api/odas/scraped-users/export"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(header().string("Content-Disposition", containsString("attachment")))
                .andExpect(header().string("Content-Disposition", containsString("candidats-scrapes_")));
    }


    @Test
    @ResetDataAfter
    void createAgefiphAccount_should_succeed_with_valid_command() throws Exception {
        var candidateId = "CAND001";
        var scrapedUserId = UUID.randomUUID();
        var command = new CreateAgefiphAccountCommandDTO()
                .candidateId(candidateId)
                .scrapedUserId(scrapedUserId);

        Mockito.doAnswer(invocation -> {
            Runnable action = invocation.getArgument(0);
            action.run();
            return null;
        }).when(securityService).doAsAdmin(Mockito.any(Runnable.class));

        performPost("/public/create-agefiph-account", command)
                .andExpect(status().isNoContent());

        Mockito.verify(scrapedUserAccountService).createAgefiphCandidateAccount(command);
    }

    private List<ScrapedUserDTO> prepareScrapedUsers() {
        var scrapedUser1 = new ScrapedUserDTO()
                .uuid(UUID.randomUUID())
                .candidateId("CAND001")
                .firstName("John")
                .lastName("Doe")
                .email("<EMAIL>")
                .jobTitle("Software Developer")
                .location("Paris")
                .cvContent("{\"email\":\"<EMAIL>\",\"firstName\":\"John\",\"lastName\":\"Doe\"}")
                .cvDownloadLink("http://example.com/cv1.pdf")
                .profileUrl("http://example.com/profile/CAND001");

        var scrapedUser2 = new ScrapedUserDTO()
                .uuid(UUID.randomUUID())
                .candidateId("CAND002")
                .firstName("Jane")
                .lastName("Smith")
                .email("<EMAIL>")
                .jobTitle("Data Analyst")
                .location("Lyon")
                .cvContent("{\"email\":\"<EMAIL>\",\"firstName\":\"Jane\",\"lastName\":\"Smith\"}")
                .cvDownloadLink("http://example.com/cv2.pdf")
                .profileUrl("http://example.com/profile/CAND002");

        return List.of(scrapedUser1, scrapedUser2);
    }

}
