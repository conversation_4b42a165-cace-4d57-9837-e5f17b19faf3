package com.erhgo.services.scrapeduser;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.domain.userprofile.ScrapedUser;
import com.erhgo.domain.userprofile.UserProfileCreationState;
import com.erhgo.openapi.dto.CreateAgefiphAccountCommandDTO;
import com.erhgo.repositories.ScrapedUserRepository;
import com.erhgo.services.HandicapAccountService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class ScrapedUserAccountServiceTest extends AbstractIntegrationTest {

    @Autowired
    private ScrapedUserAccountService scrapedUserAccountService;

    @MockitoBean
    private ScrapedUserRepository scrapedUserRepository;

    @MockitoBean
    private AgefiphScraper agefiphScraper;

    @MockitoBean
    private HandicapAccountService handicapAccountService;

    @MockitoBean
    private KeycloakMockService keycloakService;

    @MockitoBean
    private MailingListService sendInBlueService;

    @Test
    @ResetDataAfter
    void createAgefiphCandidateAccount_should_succeed_when_valid_data() throws InvalidScrapingException {
        setupAuthToken();
        var scrapedUser = createScrapedUserWithMinimalData();
        var command = createCommand(scrapedUser.getUuid(), scrapedUser.getCandidateId());

        when(scrapedUserRepository.findById(scrapedUser.getUuid())).thenReturn(Optional.of(scrapedUser));
        when(keycloakService.getFOUserRepresentationByEmail(scrapedUser.getEmail())).thenReturn(null);
        when(agefiphScraper.getCandidateDetails(scrapedUser.getCandidateId())).thenReturn(createScrapedCandidateWithFullData());

        scrapedUserAccountService.createAgefiphCandidateAccount(command);

        verify(handicapAccountService).createOrUpdateUserForFileURL(eq(scrapedUser.getEmail()), any(Optional.class));
        verify(scrapedUserRepository).save(scrapedUser);
        assertThat(scrapedUser.getCreationState()).isEqualTo(UserProfileCreationState.CREATED);

        assertThat(scrapedUser.getFirstName()).isEqualTo("John");
        assertThat(scrapedUser.getLastName()).isEqualTo("Doe");
        assertThat(scrapedUser.getJobTitle()).isEqualTo("Software Developer");
        assertThat(scrapedUser.getLocation()).isEqualTo("Paris (75)");
    }

    @Test
    @ResetDataAfter
    void createAgefiphCandidateAccount_should_throw_exception_when_scraped_user_not_found() {
        var scrapedUserId = UUID.randomUUID();
        var command = createCommand(scrapedUserId, "CAND001");

        when(scrapedUserRepository.findById(scrapedUserId)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> scrapedUserAccountService.createAgefiphCandidateAccount(command))
                .isInstanceOf(EntityNotFoundException.class);

        verifyNoInteractions(agefiphScraper, handicapAccountService);
    }

    @Test
    @ResetDataAfter
    void createAgefiphCandidateAccount_should_update_state_when_account_already_exists() {
        var scrapedUser = createScrapedUserWithMinimalData();
        var command = createCommand(scrapedUser.getUuid(), scrapedUser.getCandidateId());
        var existingUser = new UserRepresentation();

        when(scrapedUserRepository.findById(scrapedUser.getUuid())).thenReturn(Optional.of(scrapedUser));
        when(keycloakService.getFOUserRepresentationByEmail(scrapedUser.getEmail())).thenReturn(existingUser);

        scrapedUserAccountService.createAgefiphCandidateAccount(command);

        verify(scrapedUserRepository).save(scrapedUser);
        verifyNoInteractions(agefiphScraper, handicapAccountService);
        assertThat(scrapedUser.getCreationState()).isEqualTo(UserProfileCreationState.ALREADY_EXISTS);
    }

    @Test
    @ResetDataAfter
    void createAgefiphCandidateAccount_should_handle_candidate_details_fetch_error() throws InvalidScrapingException {
        setupAuthToken();
        var scrapedUser = createScrapedUserWithMinimalData();
        var command = createCommand(scrapedUser.getUuid(), scrapedUser.getCandidateId());

        when(scrapedUserRepository.findById(scrapedUser.getUuid())).thenReturn(Optional.of(scrapedUser));
        when(keycloakService.getFOUserRepresentationByEmail(scrapedUser.getEmail())).thenReturn(null);
        when(agefiphScraper.getCandidateDetails(scrapedUser.getCandidateId()))
                .thenThrow(new InvalidScrapingException("API Error"));

        scrapedUserAccountService.createAgefiphCandidateAccount(command);

        verify(scrapedUserRepository).save(scrapedUser);
        verifyNoInteractions(handicapAccountService);
        assertThat(scrapedUser.getCreationState()).isEqualTo(UserProfileCreationState.ERROR);
        assertThat(scrapedUser.getErrorMessage()).contains("Failed to retrieve candidate details");
    }

    @Test
    @ResetDataAfter
    void addCandidateToBrevoList_should_succeed_when_valid_email() {
        var scrapedUser = createScrapedUserWithMinimalData();

        scrapedUserAccountService.addCandidateToBrevoList(scrapedUser);

        verify(sendInBlueService).addContactToAgefiphCandidatesList(
                scrapedUser.getEmail(),
                scrapedUser.getUuid().toString(),
                scrapedUser.getCandidateId()
        );
        verify(scrapedUserRepository).save(scrapedUser);
        assertThat(scrapedUser.getCreationState()).isEqualTo(UserProfileCreationState.PENDING);
    }

    @Test
    @ResetDataAfter
    void addCandidateToBrevoList_should_skip_when_no_email() {
        var scrapedUser = createScrapedUserWithMinimalData();
        scrapedUser.setEmail(null);

        scrapedUserAccountService.addCandidateToBrevoList(scrapedUser);

        verifyNoInteractions(sendInBlueService, scrapedUserRepository);
    }

    private void setupAuthToken() {
        txHelper.doInTransaction(() -> {
            var sql = "INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES ('agefiph.auth.token', 'tokenxxxxx')";
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
    }

    private CreateAgefiphAccountCommandDTO createCommand(UUID scrapedUserId, String candidateId) {
        return new CreateAgefiphAccountCommandDTO()
                .candidateId(candidateId)
                .scrapedUserId(scrapedUserId);
    }


    private ScrapedUser createScrapedUserWithMinimalData() {
        return ScrapedUser.builder()
                .uuid(UUID.randomUUID())
                .candidateId("CAND001")
                .email("<EMAIL>")
                .cvDownloadLink("https://espace-emploi.agefiph.fr/api/company/candidates/CAND001/resume/download")
                .profileUrl("https://espace-emploi.agefiph.fr/api/company/candidates/CAND001")
                .creationState(UserProfileCreationState.PENDING)
                .build();
    }

    private ScrapedCandidate createScrapedCandidateWithFullData() {
        return ScrapedCandidate.builder()
                .candidateId("CAND001")
                .firstName("John")
                .lastName("Doe")
                .email("<EMAIL>")
                .jobTitle("Software Developer")
                .location("Paris (75)")
                .cvDownloadLink("https://espace-emploi.agefiph.fr/api/company/candidates/CAND001/resume/download")
                .profileUrl("https://espace-emploi.agefiph.fr/api/company/candidates/CAND001")
                .build();
    }
}
