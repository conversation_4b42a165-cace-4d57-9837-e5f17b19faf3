package com.erhgo.services.scrapeduser;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.userprofile.ScrapedUser;
import com.erhgo.domain.userprofile.UserProfileCreationState;
import com.erhgo.repositories.ScrapedUserRepository;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class ScrapedUserScrapingIntegrationTest extends AbstractIntegrationTest {

    private static final String[][] CANDIDATE_DATA = {
            {"CAND001", "<PERSON>", "Doe", "<EMAIL>", "Software Developer", "Paris (75)"},
            {"CAND002", "Jane", "Smith", "<EMAIL>", "Data Analyst", "Lyon (69)"},
            {"CAND003", "Bob", "Johnson", "<EMAIL>", "Project Manager", "Marseille (13)"},
            {"CAND004", "Alice", "Brown", "<EMAIL>", "Designer", "Nice (06)"}
    };

    @Autowired
    private ScrapedUserScheduler scrapedUserScheduler;

    @Autowired
    private ScrapedUserService scrapedUserService;

    @Autowired
    private ScrapedUserRepository scrapedUserRepository;

    @MockitoBean
    private AgefiphScraper agefiphScraper;


    @BeforeEach
    void before() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
                    VALUES ('agefiph.auth.token', 'tokenxxxxx');
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
    }

    @Test
    @ResetDataAfter
    void fullScrapingSuccess_should_store_minimal_data_for_rgpd_compliance() {
        var candidates = createTestCandidates("CAND001", "CAND002", "CAND003");

        when(agefiphScraper.searchCandidates()).thenReturn(candidates);

        scrapedUserService.processCandidates();

        txHelper.doInTransaction(() -> {
            var savedUsers = scrapedUserRepository.findAll();
            assertThat(savedUsers).hasSize(3);

            var user1 = scrapedUserRepository.findByCandidateId("CAND001").orElseThrow();
            assertThat(user1.getEmail()).isEqualTo("<EMAIL>");
            assertThat(user1.getCandidateId()).isEqualTo("CAND001");
            assertThat(user1.getCvDownloadLink()).isNotNull();
            assertThat(user1.getProfileUrl()).isNotNull();
            assertThat(user1.getCreationState()).isEqualTo(UserProfileCreationState.PENDING);

            assertThat(user1.getFirstName()).isNull();
            assertThat(user1.getLastName()).isNull();
            assertThat(user1.getJobTitle()).isNull();
            assertThat(user1.getLocation()).isNull();

            var user2 = scrapedUserRepository.findByCandidateId("CAND002").orElseThrow();
            assertThat(user2.getEmail()).isEqualTo("<EMAIL>");

            var user3 = scrapedUserRepository.findByCandidateId("CAND003").orElseThrow();
            assertThat(user3.getEmail()).isEqualTo("<EMAIL>");
        });

        verify(agefiphScraper).searchCandidates();
    }

    @Test
    @ResetDataAfter
    void scraping_should_respect_daily_limit() {
        var originalLimit = ReflectionTestUtils.getField(scrapedUserService, "batchCandidateLimit");
        ReflectionTestUtils.setField(scrapedUserService, "batchCandidateLimit", 2L);

        var candidates = createTestCandidates("CAND001", "CAND002", "CAND003");

        when(agefiphScraper.searchCandidates()).thenReturn(candidates);

        try {
            scrapedUserScheduler.processDailyCandidates();

            txHelper.doInTransaction(() -> {
                var savedUsers = scrapedUserRepository.findAll();
                assertThat(savedUsers).hasSize(2);
                assertThat(savedUsers).extracting("candidateId").containsExactlyInAnyOrder("CAND001", "CAND002");

                savedUsers.forEach(user -> {
                    assertThat(user.getEmail()).isNotNull();
                    assertThat(user.getFirstName()).isNull();
                    assertThat(user.getLastName()).isNull();
                    assertThat(user.getJobTitle()).isNull();
                    assertThat(user.getLocation()).isNull();
                });
            });
        } finally {
            ReflectionTestUtils.setField(scrapedUserService, "batchCandidateLimit", originalLimit);
        }
    }


    @Test
    @ResetDataAfter
    void scraping_should_skip_existing_candidates() {
        var existingUser = ScrapedUser.builder()
                .uuid(UUID.randomUUID())
                .candidateId("CAND001")
                .email("<EMAIL>")
                .cvDownloadLink("https://espace-emploi.agefiph.fr/api/company/candidates/CAND001/resume/download")
                .profileUrl("https://espace-emploi.agefiph.fr/api/company/candidates/CAND001")
                .creationState(UserProfileCreationState.PENDING)
                .build();
        scrapedUserRepository.save(existingUser);

        var candidates = createTestCandidates("CAND001", "CAND002");

        when(agefiphScraper.searchCandidates()).thenReturn(candidates);

        scrapedUserService.processCandidates();

        txHelper.doInTransaction(() -> {
            var savedUsers = scrapedUserRepository.findAll();
            assertThat(savedUsers).hasSize(2);
            assertThat(savedUsers).extracting("candidateId").containsExactlyInAnyOrder("CAND001", "CAND002");

            var newUser = scrapedUserRepository.findByCandidateId("CAND002").orElseThrow();
            assertThat(newUser.getEmail()).isEqualTo("<EMAIL>");
        });

        verify(agefiphScraper).searchCandidates();
    }

    @Test
    @ResetDataAfter
    void scraping_should_handle_empty_candidates_list() {
        when(agefiphScraper.searchCandidates()).thenReturn(List.of());

        scrapedUserService.processCandidates();

        verify(agefiphScraper).searchCandidates();

        assertThat(scrapedUserRepository.findAll()).isEmpty();
    }

    private List<ScrapedCandidate> createTestCandidates(String... candidateIds) {
        return Arrays.stream(candidateIds)
                .map(id -> Arrays.stream(CANDIDATE_DATA)
                        .filter(data -> data[0].equals(id))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException("Unknown candidate ID: " + id)))
                .map(data -> createTestCandidate(data[0], data[1], data[2], data[3], data[4], data[5]))
                .toList();
    }

    private ScrapedCandidate createTestCandidate(String candidateId, String firstName, String lastName, String email, String jobTitle, String location) {
        return ScrapedCandidate.builder()
                .candidateId(candidateId)
                .firstName(firstName)
                .lastName(lastName)
                .email(email)
                .jobTitle(jobTitle)
                .hasResume(true)
                .location(location)
                .cvDownloadLink("https://espace-emploi.agefiph.fr/api/company/candidates/" + candidateId + "/resume/download")
                .profileUrl("https://espace-emploi.agefiph.fr/api/company/candidates/" + candidateId)
                .build();
    }
}
