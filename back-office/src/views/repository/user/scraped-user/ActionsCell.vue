<template>
  <td style="width: 15%">
    <v-tooltip top>
      <template v-slot:activator="{ on }">
        <v-btn icon small v-on="on" color="primary" @click="showDetails">
          <v-icon>visibility</v-icon>
        </v-btn>
      </template>
      <span>Voir les détails</span>
    </v-tooltip>

    <v-tooltip top>
      <template v-slot:activator="{ on }">
        <v-btn
          icon
          small
          color="secondary"
          v-on="on"
          @click="createAgefiphAccount"
          :loading="createAccountLoading"
          :disabled="!canCreateAccount"
        >
          <v-icon>mdi-account-plus</v-icon>
        </v-btn>
      </template>
      <span>Créer un utilisateur</span>
    </v-tooltip>

    <v-tooltip top v-if="item.profileUrl">
      <template v-slot:activator="{ on }">
        <v-btn
          icon
          small
          color="info"
          v-on="on"
          :href="item.profileUrl"
          target="_blank"
        >
          <v-icon>mdi-open-in-new</v-icon>
        </v-btn>
      </template>
      <span>Voir le profil source</span>
    </v-tooltip>
  </td>
</template>

<script>
export default {
  name: 'ScrapedUserActionsCell',
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      createAccountLoading: false,
    };
  },
  computed: {
    canCreateAccount() {
      return this.item.candidateId && this.item.cvDownloadLink;
    },
  },
  methods: {
    showDetails() {
      this.$emit('show-details', this.item);
    },
    async createAgefiphAccount() {
      if (!this.canCreateAccount) return;

      this.createAccountLoading = true;
      try {
        await this.$api.createAgefiphAccount({
          candidateId: this.item.candidateId,
          scrapedUserId: this.item.uuid,
        });
      } finally {
        this.createAccountLoading = false;
      }
    },
  },
};
</script>

<style scoped></style>
